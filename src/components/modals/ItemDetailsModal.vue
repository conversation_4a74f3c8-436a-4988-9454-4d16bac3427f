<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-2">
            <div
              class="w-4 h-4 rounded-full"
              :class="{
                'bg-blue-500': item.color === 'blue',
                'bg-red-500': item.color === 'red',
                'bg-green-500': item.color === 'green'
              }"
            ></div>
            <h3 class="text-lg font-medium text-gray-900">{{ item.title }}</h3>
          </div>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
            <p class="text-sm text-gray-900">{{ formatDate(item.date) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <span
              class="inline-flex px-2 py-1 text-xs font-medium rounded-full capitalize"
              :class="{
                'bg-blue-100 text-blue-800': item.color === 'blue',
                'bg-red-100 text-red-800': item.color === 'red',
                'bg-green-100 text-green-800': item.color === 'green'
              }"
            >
              {{ item.type.replace('-', ' ') }}
            </span>
          </div>

          <div v-if="eventDetails">
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <p class="text-sm text-gray-900">{{ eventDetails.description || 'No description available' }}</p>
          </div>

          <div v-if="equipmentDetails">
            <label class="block text-sm font-medium text-gray-700 mb-1">Equipment Details</label>
            <div class="bg-gray-50 p-3 rounded-md">
              <p class="text-sm font-medium text-gray-900">{{ equipmentDetails.name }}</p>
              <p class="text-xs text-gray-600">Model: {{ equipmentDetails.model }}</p>
              <p class="text-xs text-gray-600">Serial: {{ equipmentDetails.serialNumber }}</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-6">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
          <button
            v-if="item.type === 'event'"
            @click="editEvent"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Edit Event
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// import { useEventsStore } from '@/stores/events'
// import { useEquipmentStore } from '@/stores/equipment'
import type { CalendarItem } from '@/types'

const props = defineProps<{
  item: CalendarItem
}>()

const emit = defineEmits<{
  close: []
}>()

// const eventsStore = useEventsStore()
// const equipmentStore = useEquipmentStore()

const eventDetails = computed(() => {
  if (props.item.type === 'event' || props.item.type === 'reminder') {
    return eventsStore.getEventById(props.item.id)
  }
  return null
})

const equipmentDetails = computed(() => {
  if (props.item.type === 'equipment-expiry' && props.item.relatedId) {
    return equipmentStore.getEquipmentById(props.item.relatedId)
  }
  return null
})

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const editEvent = () => {
  // TODO: Implement edit event functionality
  console.log('Edit event:', props.item.id)
  emit('close')
}
</script>
